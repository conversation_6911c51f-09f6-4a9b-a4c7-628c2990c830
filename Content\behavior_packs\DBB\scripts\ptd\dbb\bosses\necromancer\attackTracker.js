// Available attacks for each range
export const SHORT_RANGE_ATTACKS = ["cataclysm", "book_of_the_damned", "phantom_phase_start", "undead_summon", "soul_hands"];
export const MEDIUM_RANGE_ATTACKS = [
    "cataclysm",
    "book_of_the_damned",
    "soul_drain",
    "phantom_phase_start",
    "undead_summon",
    "arcane_blast",
    "soul_hands"
];
export const LONG_RANGE_ATTACKS = [
    "cataclysm",
    "book_of_the_damned",
    "soul_drain",
    "phantom_phase_start",
    "undead_summon",
    "arcane_blast",
    "soul_hands"
];
export const UNREACHABLE_RANGE_ATTACKS = [
    "soul_drain",
    "phantom_phase_start",
    "undead_summon",
    "arcane_blast",
    "soul_hands"
];
// Dynamic property names
const SHORT_RANGE_HISTORY_PROP = "shortRangeAttackHistory";
const MEDIUM_RANGE_HISTORY_PROP = "mediumRangeAttackHistory";
const LONG_RANGE_HISTORY_PROP = "longRangeAttackHistory";
const UNREACHABLE_RANGE_HISTORY_PROP = "unreachableRangeAttackHistory";
/**
 * Initialize attack history for a range
 * @param entity The entity to initialize attack history for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @param attacks The available attacks for the range
 */
function initializeAttackHistory(entity, rangeName, attacks) {
    const history = {};
    for (const attack of attacks) {
        history[attack] = 0;
    }
    // Set the dynamic property
    const propName = getRangePropertyName(rangeName);
    entity.setDynamicProperty(propName, JSON.stringify(history));
}
/**
 * Get the dynamic property name for a range
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @returns The dynamic property name
 */
function getRangePropertyName(rangeName) {
    switch (rangeName) {
        case "short":
            return SHORT_RANGE_HISTORY_PROP;
        case "medium":
            return MEDIUM_RANGE_HISTORY_PROP;
        case "long":
            return LONG_RANGE_HISTORY_PROP;
        case "unreachable":
            return UNREACHABLE_RANGE_HISTORY_PROP;
        default:
            return "";
    }
}
/**
 * Get available attacks for a range
 * @param entity The entity to get available attacks for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @param attacks The available attacks for the range
 * @returns The available attacks based on usage history
 */
export function getAvailableAttacks(entity, rangeName, attacks) {
    const propName = getRangePropertyName(rangeName);
    const historyJson = entity.getDynamicProperty(propName);
    // Initialize attack history if it doesn't exist
    if (!historyJson) {
        initializeAttackHistory(entity, rangeName, attacks);
        return [...attacks]; // Return a copy of the attacks array
    }
    const history = JSON.parse(historyJson);
    // Check if all attacks have been used twice
    const allUsedTwice = attacks.every((attack) => (history[attack] ?? 0) >= 2);
    if (allUsedTwice) {
        // Reset history if all attacks have been used twice
        initializeAttackHistory(entity, rangeName, attacks);
        return [...attacks]; // Return a copy of the attacks array
    }
    // Check if any attack hasn't been used yet
    const unusedAttacks = attacks.filter((attack) => (history[attack] ?? 0) === 0);
    if (unusedAttacks.length > 0) {
        return unusedAttacks;
    }
    // If all attacks have been used at least once, return attacks used less than twice
    return attacks.filter((attack) => (history[attack] ?? 0) < 2);
}
/**
 * Update attack history after an attack is selected
 * @param entity The entity to update attack history for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @param attack The selected attack
 */
export function updateAttackHistory(entity, rangeName, attack) {
    const propName = getRangePropertyName(rangeName);
    const historyJson = entity.getDynamicProperty(propName);
    if (!historyJson) {
        return;
    }
    const history = JSON.parse(historyJson);
    history[attack] = (history[attack] ?? 0) + 1;
    entity.setDynamicProperty(propName, JSON.stringify(history));
}
/**
 * Get the attack history for a range
 * @param entity The entity to get attack history for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 * @returns The attack history as a Record<string, number> or null if not found
 */
export function getAttackHistory(entity, rangeName) {
    const propName = getRangePropertyName(rangeName);
    const historyJson = entity.getDynamicProperty(propName);
    if (!historyJson) {
        return null;
    }
    return JSON.parse(historyJson);
}
/**
 * Display attack history on the actionbar
 * @param entity The entity to get attack history for
 * @param rangeName The name of the range (short, medium, long, unreachable)
 */
export function displayAttackHistory(entity, rangeName) {
    const history = getAttackHistory(entity, rangeName);
    if (!history) {
        return;
    }
    // For now, we don't display the history, but this function can be expanded in the future
}
