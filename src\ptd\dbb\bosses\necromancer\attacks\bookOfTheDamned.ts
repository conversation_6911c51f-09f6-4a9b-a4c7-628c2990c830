import { Entity, EntityComponentTypes, system, Vector3 } from "@minecraft/server";
import { getTarget } from "../../general_mechanics/targetUtils";
import { getDirection } from "../../../utilities/vector3";
import { faceDirection } from "../../../entities/projectileRotation";

/**
 * Configuration for the book of the damned attack
 */
const BOOK_OF_THE_DAMNED_CONFIG = {
  /** Projectile entity type */
  PROJECTILE_TYPE: "ptd_dbb:flying_skull",
  /** Projectile speed (velocity multiplier) */
  PROJECTILE_SPEED: 0.3,
  /** Tick interval for applying impulse */
  IMPULSE_INTERVAL: 1,
  /** Vertical offset from necromancer position */
  VERTICAL_OFFSET: -0.4,
  /** Delay between firing skulls in ticks */
  SKULL_FIRE_DELAY: 10
};

/**
 * Executes the book of the damned attack for the Necromancer
 * Fires rapid succession of flying skull projectiles at the target
 * Uses the same timing as cataclysm but fires skulls instead of expanding damage radius
 *
 * @param necromancer The necromancer entity
 * @param attackTimer The current attack timer
 * @returns void
 */
export function executeBookOfTheDamnedAttack(necromancer: Entity, attackTimer: number): void {
  try {
    // Attack starts at 3.5 seconds (70 ticks) and ends at 7.5 seconds (150 ticks)
    // Total duration of damage phase: 4 seconds (80 ticks)
    if (attackTimer < 70 || attackTimer > 150) {
      return; // Not in the damage phase
    }

    // Calculate which skull to fire based on the timer
    // Fire a skull every 5 ticks starting from tick 70
    const ticksSinceStart = attackTimer - 70;
    
    // Only fire on specific ticks (every 5 ticks)
    if (ticksSinceStart % BOOK_OF_THE_DAMNED_CONFIG.SKULL_FIRE_DELAY !== 0) {
      return;
    }

    // Find the target to determine direction
    const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
    if (!target) return;

    const headLoc = necromancer.getHeadLocation();
    const viewDir = necromancer.getViewDirection();

    // Spawn the flying skull projectile at the chest level of the necromancer
    const spawnPos: Vector3 = {
      x: headLoc.x + viewDir.x * 3,
      y: headLoc.y + BOOK_OF_THE_DAMNED_CONFIG.VERTICAL_OFFSET,
      z: headLoc.z + viewDir.z * 3
    };

    // Get the target location with a y-offset
    const targetLoc: Vector3 = {
      x: target.getHeadLocation().x,
      y: target.getHeadLocation().y - 0.2,
      z: target.getHeadLocation().z
    };

    // Calculate direction toward the target (already normalized)
    const direction = getDirection(spawnPos, targetLoc);

    // Calculate velocity based on direction and speed
    const velocity: Vector3 = {
      x: direction.x * BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_SPEED,
      y: direction.y * BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_SPEED,
      z: direction.z * BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_SPEED
    };

    // Spawn the flying skull entity
    const projectile = necromancer.dimension.spawnEntity(BOOK_OF_THE_DAMNED_CONFIG.PROJECTILE_TYPE, spawnPos);

    if (!projectile) return;

    // Apply initial impulse
    const projectileComponent = projectile.getComponent(EntityComponentTypes.Projectile);
    if (!projectileComponent) return;

    // Apply initial impulse
    projectileComponent.shoot(velocity);

    // Set up interval to continuously apply impulse in the SAME direction
    const intervalId = system.runInterval(() => {
      try {
        // Check if entity is still valid/exists
        if (!projectile) {
          system.clearRun(intervalId);
          return;
        }

        // Apply impulse again using the SAME velocity vector
        const projectileComponent = projectile.getComponent(EntityComponentTypes.Projectile);
        if (projectileComponent) {
          projectileComponent.shoot(velocity);
        }

        // Constantly face toward the direction
        faceDirection(projectile, spawnPos, targetLoc);
      } catch (error) {
        // If any error occurs (likely because projectile no longer exists)
        system.clearRun(intervalId);
      }
    }, BOOK_OF_THE_DAMNED_CONFIG.IMPULSE_INTERVAL);

    // Play a sound effect for each skull fired
    necromancer.dimension.playSound("mob.blaze.shoot", headLoc);
  } catch (error) {
    console.warn(`Error in book of the damned attack: ${error}`);
  }
}
