import { getDistance } from "../../utilities/vector3";
import { executeCataclysmAttack } from "./attacks/cataclysm";
import { executeBookOfTheDamnedAttack } from "./attacks/bookOfTheDamned";
import { executeSoulDrainAttack } from "./attacks/soulDrain";
import { executePhantomPhaseAbility } from "./abilities/phantomPhase";
import { executeUndeadSummonAttack } from "./attacks/undeadSummon";
import { executeArcaneBlastAttack } from "./attacks/arcaneBlast";
import { executeSoulHandsAttack } from "./attacks/soulHands";
import { stopNecromancerSounds } from "./soundManager";
import { getAvailableAttacks, updateAttackHistory, displayAttackHistory, SHORT_RANGE_ATTACKS, MEDIUM_RANGE_ATTACKS, LONG_RANGE_ATTACKS, UNREACHABLE_RANGE_ATTACKS } from "./attackTracker";
import { getTarget } from "../general_mechanics/targetUtils";
/**
 * Attack ranges for different attack types
 */
export const ATTACK_RANGES = {
    close: { min: 0, max: 5 },
    medium: { min: 5, max: 10 },
    long: { min: 10, max: 16 },
    unreachable: { min: 16, max: 32 }
};
/**
 * Attack durations in ticks
 */
export const ATTACK_DURATIONS = {
    cataclysm: 200, // 10 seconds at 20 ticks per second
    book_of_the_damned: 200, // 10 seconds at 20 ticks per second (same as cataclysm)
    soul_drain: 116, // Total attack duration is 116 ticks
    phantom_phase_start: 37, // Updated to match animation length - 9 extra ticks for smooth transition to the next animation
    phantom_phase_end: 95, // Updated to offset the animation length
    undead_summon: 115, // Total attack duration is 115 ticks
    arcane_blast: 100,
    soul_hands: 90
};
/**
 * Attack timing points in ticks
 * When the damage and effects should be applied
 */
export const ATTACK_TIMINGS = {
    cataclysm_start: 70, // 3.5 seconds
    cataclysm_end: 150, // 7.5 seconds
    book_of_the_damned_start: 70, // 3.5 seconds (same as cataclysm)
    book_of_the_damned_end: 150, // 7.5 seconds (same as cataclysm)
    soul_drain: 42, // 2.1 seconds
    phantom_phase_start: 37, // Teleport at the actual end of phase_start
    phantom_phase_end: 0, // No timing
    undead_summon: 40, // Start summoning zombies at 40 ticks
    arcane_blast: 50, // Fire skull at 50 ticks
    soul_hands: 45 // Summon skeleton souls at 45 ticks
};
/**
 * Sound effects for each attack type
 */
export const ATTACK_SOUND_MAP = {
    cataclysm: "mob.ptd_dbb_necromancer.cataclysm",
    book_of_the_damned: "mob.ptd_dbb_necromancer.cataclysm",
    soul_drain: "mob.ptd_dbb_necromancer.soul_drain",
    phantom_phase_start: "mob.ptd_dbb_necromancer.phantom_phase",
    phantom_phase_end: "mob.ptd_dbb_necromancer.phantom_phase",
    undead_summon: "mob.ptd_dbb_necromancer.undead_summon",
    arcane_blast: "mob.ptd_dbb_necromancer.arcane_blast",
    soul_hands: "mob.ptd_dbb_necromancer.soul_hands"
};
/**
 * Selects an attack based on target distance
 * @param necromancer The necromancer entity
 * @param target The target entity
 */
export function selectAttack(necromancer, target) {
    // Check if the attack cooldown is 0
    const attackCooldown = necromancer.getProperty("ptd_dbb:attack_cooldown");
    if (attackCooldown > 0) {
        return; // Don't select an attack if the cooldown is not 0
    }
    const distance = getDistance(necromancer.location, target.location);
    let selectedAttack = null;
    // Short range (0-5 blocks): select from available attacks based on usage history
    if (distance >= ATTACK_RANGES.close.min && distance <= ATTACK_RANGES.close.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(necromancer, "short", SHORT_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopNecromancerSounds(necromancer, target, attackSound);
            // Trigger the attack event
            necromancer.triggerEvent(`ptd_dbb:${attack}_attack`);
            // Set the attack property
            necromancer.setProperty("ptd_dbb:attack", attack);
            necromancer.setProperty("ptd_dbb:attack_timer", 0);
            // Update attack history
            updateAttackHistory(necromancer, "short", attack);
            // Display attack history on the actionbar
            displayAttackHistory(necromancer, "short");
        }
    }
    // Medium range (5-10 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.medium.min && distance <= ATTACK_RANGES.medium.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(necromancer, "medium", MEDIUM_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopNecromancerSounds(necromancer, target, attackSound);
            // Trigger the attack event
            necromancer.triggerEvent(`ptd_dbb:${attack}_attack`);
            // Set the attack property
            necromancer.setProperty("ptd_dbb:attack", attack);
            necromancer.setProperty("ptd_dbb:attack_timer", 0);
            // Update attack history
            updateAttackHistory(necromancer, "medium", attack);
            // Display attack history on the actionbar
            displayAttackHistory(necromancer, "medium");
        }
    }
    // Long range (10-16 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.long.min && distance <= ATTACK_RANGES.long.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(necromancer, "long", LONG_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopNecromancerSounds(necromancer, target, attackSound);
            // Trigger the attack event
            necromancer.triggerEvent(`ptd_dbb:${attack}_attack`);
            // Set the attack property
            necromancer.setProperty("ptd_dbb:attack", attack);
            necromancer.setProperty("ptd_dbb:attack_timer", 0);
            // Update attack history
            updateAttackHistory(necromancer, "long", attack);
            // Display attack history on the actionbar
            displayAttackHistory(necromancer, "long");
        }
    }
    // Unreachable range (16-32 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.unreachable.min && distance <= ATTACK_RANGES.unreachable.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(necromancer, "unreachable", UNREACHABLE_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopNecromancerSounds(necromancer, target, attackSound);
            // Trigger the attack event
            necromancer.triggerEvent(`ptd_dbb:${attack}_attack`);
            // Set the attack property
            necromancer.setProperty("ptd_dbb:attack", attack);
            necromancer.setProperty("ptd_dbb:attack_timer", 0);
            // Update attack history
            updateAttackHistory(necromancer, "unreachable", attack);
            // Display attack history on the actionbar
            displayAttackHistory(necromancer, "unreachable");
        }
    }
    // Apply slowness effect if an attack was selected
    if (selectedAttack && selectedAttack in ATTACK_DURATIONS) {
        const attackDuration = ATTACK_DURATIONS[selectedAttack];
        if (attackDuration) {
            // Apply slowness with amplifier 250 for the duration of the attack
            // This effectively prevents movement during the attack
            necromancer.addEffect("minecraft:slowness", attackDuration, { amplifier: 250, showParticles: false });
        }
    }
}
/**
 * Handles attack logic based on attack type and timer
 * @param necromancer The necromancer entity
 * @param attack The current attack type
 * @param attackTimer The current attack timer
 */
export function handleAttackLogic(necromancer, attack, attackTimer) {
    // Execute cataclysm attack during the damage phase
    if (attack === "cataclysm" &&
        ATTACK_TIMINGS["cataclysm_start"] !== undefined &&
        ATTACK_TIMINGS["cataclysm_end"] !== undefined &&
        attackTimer >= ATTACK_TIMINGS["cataclysm_start"] &&
        attackTimer <= ATTACK_TIMINGS["cataclysm_end"]) {
        executeCataclysmAttack(necromancer, attackTimer);
    }
    // Execute book of the damned attack during the damage phase
    else if (attack === "book_of_the_damned" &&
        ATTACK_TIMINGS["book_of_the_damned_start"] !== undefined &&
        ATTACK_TIMINGS["book_of_the_damned_end"] !== undefined &&
        attackTimer >= ATTACK_TIMINGS["book_of_the_damned_start"] &&
        attackTimer <= ATTACK_TIMINGS["book_of_the_damned_end"]) {
        executeBookOfTheDamnedAttack(necromancer, attackTimer);
    }
    // Execute soul drain attack at the specified timing point
    else if (attack === "soul_drain" &&
        ATTACK_TIMINGS["soul_drain"] !== undefined &&
        attackTimer === ATTACK_TIMINGS["soul_drain"]) {
        // First try to get the target using the getTarget function
        const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
        // If we have a valid target, execute the soul drain attack
        if (target) {
            executeSoulDrainAttack(necromancer, target);
        }
    }
    // Execute phantom phase ability
    else if ((attack === "phantom_phase_start" || attack === "phantom_phase_end") &&
        ATTACK_TIMINGS["phantom_phase_start"] !== undefined) {
        executePhantomPhaseAbility(necromancer, attackTimer, attack);
    }
    // Execute undead summon attack at the specified timing point
    else if (attack === "undead_summon" &&
        ATTACK_TIMINGS["undead_summon"] !== undefined &&
        attackTimer === ATTACK_TIMINGS["undead_summon"]) {
        executeUndeadSummonAttack(necromancer);
    }
    // Execute arcane blast attack at the specified timing point
    else if (attack === "arcane_blast" &&
        ATTACK_TIMINGS["arcane_blast"] !== undefined &&
        attackTimer === ATTACK_TIMINGS["arcane_blast"]) {
        executeArcaneBlastAttack(necromancer);
    }
    // Execute soul hands attack at the specified timing point
    else if (attack === "soul_hands" &&
        ATTACK_TIMINGS["soul_hands"] !== undefined &&
        attackTimer === ATTACK_TIMINGS["soul_hands"]) {
        executeSoulHandsAttack(necromancer);
    }
    // Reset attack when animation is complete
    const duration = ATTACK_DURATIONS[attack];
    if (duration !== undefined && attackTimer >= duration) {
        // Special case for phantom_phase_start - don't reset to none
        // This allows the phantom_phase_end_attack event to properly transition to phantom_phase_end
        if (attack === "phantom_phase_start") {
            // Don't reset the attack, let the phantom_phase_end_attack event handle the transition
            return;
        }
        // Stop all sounds when resetting attack
        stopNecromancerSounds(necromancer);
        // Reset attack and set cooldown
        necromancer.triggerEvent("ptd_dbb:reset_attack");
    }
}
